# Headers to include with all HTTP request
header:
  - 'X-BugBounty-Hacker: fastscan'

# Directory based template execution - FIXED
templates:
  - /opt/templates # custom templates are extracted to subdirectory

# Lambda-specific configurations
# Set home directory to /tmp (only writable directory in Lambda)
home: /tmp

# Disable cloud upload warnings
cloud-upload: false
disable-update-check: true

# Add explicit template configuration
update-templates: false

# Output configuration
json-export: true
no-color: true

# Performance settings
rate-limit: 500
bulk-size: 50
concurrency: 50
timeout: 10
retries: 0